language: android
sudo: required

android:
  components:
    - tools
    - platform-tools
    - build-tools-28.0.3
    - android-28
    - extra-google-m2repository
    - extra-android-m2repository
    - extra-android-support

jdk:
    - oraclejdk8

before_install:
    - mkdir "$ANDROID_HOME/licenses" || true
    - echo -e "8933bad161af4178b1185d1a37fbf41ea5269c55" > "$ANDROID_HOME/licenses/android-sdk-license"
    - echo -e "d56f5187479451eabf01fb78af6dfcb131a6481e" > "$ANDROID_HOME/licenses/android-sdk-license"

install:
    - sdkmanager --list || true
    - echo yes | sdkmanager "tools"
    - echo yes | sdkmanager "platforms;android-28"
    - echo yes | sdkmanager "extras;google;m2repository"
    - echo yes | sdkmanager "extras;android;m2repository"
    - sdkmanager --list || true

script:
    - sh gradle/enable_alirepo.sh false
    - ./gradlew clean build jacocoTestReport

notifications:
    email:
        - <EMAIL>

before_deploy:
    - sh gradle/github_release_support.sh

deploy:
    - provider: script
      script: echo deploying to jcenter... && ./gradlew clean build bintrayUpload -PbintrayUser=kyson -PbintrayKey=$BINTRAY_KEY -PdryRun=false && echo deployed to jcenter.
      on:
        tags: true
        branch: master
    - provider: releases
      api_key: $GITHUB_TOKEN
      file_glob: true
      file: github_release/*
      skip_cleanup: true
      on:
        tags: true
        branch: master

after_success:
    - bash <(curl -s https://codecov.io/bash) && echo I am done, Release url:https://github.com/Kyson/AndroidGodEye/releases and CodeCov:https://codecov.io/gh/Kyson/AndroidGodEye