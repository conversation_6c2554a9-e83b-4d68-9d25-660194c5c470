apply plugin: 'com.android.library'

apply from: rootProject.file('gradle/gradle-jacoco-support.gradle')

android {
    compileSdkVersion Integer.parseInt(COMPILE_SDK_VERSION)
    buildToolsVersion BUILD_TOOLS_VERSION

    defaultConfig {
        minSdkVersion Integer.parseInt(MIN_SDK_VERSION)
        targetSdkVersion Integer.parseInt(TARGET_SDK_VERSION)
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }
    compileOptions {
        targetCompatibility = "8"
        sourceCompatibility = "8"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.annotation:annotation:1.1.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.2'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    implementation 'com.squareup.leakcanary:shark:2.2'
    implementation "cn.hikyson.methodcanary:lib:${VERSION_METHOD_CANARY}"
    testImplementation 'junit:junit:4.12'
    testImplementation 'androidx.test:core:1.2.0'
    testImplementation 'org.mockito:mockito-core:2.8.9'
    testImplementation 'org.robolectric:robolectric:4.3'
    testImplementation "androidx.fragment:fragment-testing:1.1.0"
    testImplementation "org.powermock:powermock-module-junit4:1.7.3"
    testImplementation "org.powermock:powermock-module-junit4-rule:1.7.3"
    testImplementation "org.powermock:powermock-api-mockito2:1.7.3"
    testImplementation "org.powermock:powermock-classloading-xstream:1.7.3"
    androidTestImplementation 'androidx.test:core:1.2.0'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
}

apply from: rootProject.file('gradle/gradle-jcenter-push.gradle')