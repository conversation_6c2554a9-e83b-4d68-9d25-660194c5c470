<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="cn.hikyson.godeye.core">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application>
        <provider
            android:name=".GodEyeInitContentProvider"
            android:authorities="${applicationId}.cn.hikyson.godeye.core.init"
            android:enabled="true"
            android:exported="false" />

        <service
            android:name=".internal.notification.LocalNotificationListenerService"
            android:enabled="true"
            android:exported="false" />

    </application>

</manifest>