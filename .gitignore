# Built application files
*.apk
*.ap_

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# Intellij
*.iml
.idea/*
#.idea/workspace.xml
#.idea/tasks.xml
#.idea/gradle.xml
#.idea/dictionaries
#.idea/libraries

# Keystore files
*.jks

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# VERSION FOR MAVEN
VERSION
android-godeye-monitor/src/main/assets/.idea
android-godeye-monitor/src/main/assets/android-god-eye/.idea/workspace.xml
android-godeye-monitor/src/main/assets/android-god-eye/.idea/inspectionProfiles/profiles_settings.xml
android-godeye-monitor/src/main/assets/androidgodeye/.idea/preferred-vcs.xml
android-godeye-monitor/src/main/assets/androidgodeye/.idea/workspace.xml
android-godeye-monitor/src/main/assets/androidgodeye/.idea/inspectionProfiles/profiles_settings.xml
android-godeye-monitor/src/main/assets/android-god-eye/.idea/misc.xml
android-godeye-monitor/src/main/assets/android-god-eye/.idea/modules.xml
android-godeye-monitor/src/main/assets/android-god-eye/.idea/preferred-vcs.xml
android-godeye-monitor-dashboard/.idea/misc.xml
android-godeye-monitor-dashboard/.idea/modules.xml
android-godeye-monitor-dashboard/.idea/workspace.xml
android-godeye-monitor-dashboard/.idea/preferred-vcs.xml
android-godeye-monitor-dashboard/.idea/inspectionProfiles/Project_Default.xml
android-godeye-monitor-dashboard/.idea
android-godeye-idea-plugin/.idea/misc.xml
android-godeye-idea-plugin/.idea/modules.xml
android-godeye-idea-plugin/.idea/workspace.xml
.project
.settings/org.eclipse.buildship.core.prefs
android-godeye/.classpath
android-godeye/.project
android-godeye-monitor/.classpath
android-godeye-monitor/.project
android-godeye-monitor-no-op/.classpath
android-godeye-monitor-no-op/.project
android-godeye-monitor-no-op/.settings/org.eclipse.buildship.core.prefs
android-godeye-monitor/.settings/org.eclipse.buildship.core.prefs
android-godeye-sample/.classpath
android-godeye-sample/.project
android-godeye-sample/.settings/org.eclipse.buildship.core.prefs
android-godeye-toolbox/.classpath
android-godeye-toolbox/.project
android-godeye-toolbox/.settings/org.eclipse.buildship.core.prefs
android-godeye/.settings/org.eclipse.buildship.core.prefs
uiDesigner.xml
android-godeye-toolboxes/.settings
android-godeye-toolboxes/android-godeye-okhttp/.settings
android-godeye-toolboxes/android-godeye-xcrash/.settings
android-godeye-toolboxes/android-godeye-okhttp/.classpath
android-godeye-toolboxes/android-godeye-xcrash/.classpath
github_release
