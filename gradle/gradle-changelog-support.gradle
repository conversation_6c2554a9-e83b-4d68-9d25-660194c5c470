/**
 * Usage: ./gradlew generateChangelog -PchangelogVersion=3.1.12
 */
task generateChangelog() {
    doLast {
        def messages = getMessagesAfterCommit(getLastTagCommitSha())
        addNewVersionToChangelog(rootProject, changelogVersion, messages)
    }
}

generateChangelog.onlyIf { project.hasProperty('changelogVersion') }

///**
// * generate CHANGELOG_3_2_1.md
// */
//task generateChangelogOfNewVersion() {
//    doLast {
//        ChangelogMDFile changelogMDFile = new ChangelogMDFile()
//        changelogMDFile.parseFromMD(rootProject.file("CHANGELOG.md"))
//        Version newVersionName = changelogMDFile.getNewVersionName()
//        String changelogOfNewVersion = changelogMDFile.getChangelogOfNewVersion()
//        def changelogFile = rootProject.file("CHANGELOG_" + newVersionName.toStringOfFileName() + ".md")
//        changelogFile.write(changelogOfNewVersion, "utf-8")
//    }
//}

def static getLastTagCommitSha() {
    def lastTagSha = 'git rev-list --tags --max-count=1'.execute().text.trim()
    def lastTagName = "git describe --tags ${lastTagSha}".execute().text.trim()
    println "[CHANGELOG] Get last tag name: " + lastTagName + ", sha: " + lastTagSha
    return lastTagSha
}

def static getMessagesAfterCommit(String commitSha) {
    // git --no-pager log f61e4f8585c7777ed1d5da55d56d986c278edfb7.. --grep=#.*# --pretty=tformat:%B
    def messages = "git --no-pager log ${commitSha}.. --grep=#.*# --pretty=tformat:%B".execute().text.trim()
    println "[CHANGELOG] Changes after " + commitSha + "\n>>>>>>>>>>\n" + messages + "\n<<<<<<<<<<"
    return messages
}

/**
 * #Changed#
 * View canary ui change(always show border)
 *
 * #Added#
 * Unit test
 *
 * #Fixed#
 * Unit test failed
 * @param gitMessages
 */
def static addNewVersionToChangelog(Project rootProject, String version, String gitMessages) {
    GitMessageOfChangelog gitMessageOfChangelog = new GitMessageOfChangelog(gitMessages)
    ChangelogMDFile changelogMDFile = new ChangelogMDFile()
    changelogMDFile.parseFromMD(rootProject.file("CHANGELOG.md"))
    println "[CHANGELOG] Parsed CHANGELOG.md."
    changelogMDFile.addVersionChangelog(version, gitMessageOfChangelog)
    println "[CHANGELOG] Added new version messages to CHANGELOG.md."
    changelogMDFile.writeToMD(rootProject.file("CHANGELOG.md"))
    println "[CHANGELOG] Generated new CHANGELOG.md."
}

class ChangelogMDFile {

    @Override
    public String toString() {
        return "ChangelogMDFile{" +
                "changelogVersionSectionWithContentList=" + changelogVersionSectionWithContentList +
                '}';
    }

    public class ChangelogVersionSectionWithContent {
        public Version version
        public List<ChangelogTypeWithContent> contents

        ChangelogVersionSectionWithContent(Version version, List<ChangelogTypeWithContent> contents) {
            this.version = version
            this.contents = contents
        }


        @Override
        public String toString() {
            return "ChangelogVersionSectionWithContent{" +
                    "version=" + version +
                    ", contents=" + contents +
                    '}';
        }
    }

    public class ChangelogTypeWithContent {
        public ChangelogType changelogType
        public List<String> changelogContents

        ChangelogTypeWithContent(ChangelogType changelogType, List<String> changelogContents) {
            this.changelogType = changelogType
            this.changelogContents = changelogContents
        }

        @Override
        public String toString() {
            return "ChangelogTypeWithContent{" +
                    "changelogType=" + changelogType +
                    ", changelogContents=" + changelogContents +
                    '}';
        }
    }

    public List<ChangelogVersionSectionWithContent> changelogVersionSectionWithContentList

    public void parseFromMD(File file) {
        List<String> lines = file.readLines()
        changelogVersionSectionWithContentList = new LinkedList<>()
        ChangelogVersionSectionWithContent currentChangelogVersionSectionWithContent
        ChangelogTypeWithContent currentChangelogTypeWithContent
        for (String line : lines) {
            line = line.trim()
            if (line.startsWith("##") && !line.startsWith("###")) {
                ChangelogVersionSectionWithContent changelogVersionSectionWithContent = new ChangelogVersionSectionWithContent(new Version(line.substring(2).trim()), new ArrayList<>())
                changelogVersionSectionWithContentList.add(changelogVersionSectionWithContent)
                currentChangelogVersionSectionWithContent = changelogVersionSectionWithContent
//                println "[CHANGELOG] parseFromMD get section line: " + line + ",currentChangelogVersionSectionWithContent:" + currentChangelogVersionSectionWithContent
            } else if (line.startsWith("###") && !line.startsWith("####")) {
                if (currentChangelogVersionSectionWithContent != null) {
                    ChangelogTypeWithContent changelogTypeWithContent = new ChangelogTypeWithContent(ChangelogType.valueOf(line.substring(3).trim()), new ArrayList<>())
                    currentChangelogVersionSectionWithContent.contents.add(changelogTypeWithContent)
                    currentChangelogTypeWithContent = changelogTypeWithContent
//                    println "[CHANGELOG] parseFromMD get type line: " + line + ",currentChangelogTypeWithContent:" + currentChangelogTypeWithContent
                } else {
//                    println "[CHANGELOG] parseFromMD WARNING! get type line but version is null: " + line
                }
            } else if (line.startsWith("-")) {
                if (currentChangelogTypeWithContent != null) {
                    currentChangelogTypeWithContent.changelogContents.add(line.substring(1).trim())
//                    println "[CHANGELOG] parseFromMD get content line: " + line
                } else {
//                    println "[CHANGELOG] parseFromMD WARNING! get content line but type is null: " + line
                }
            } else {
//                println "[CHANGELOG] parseFromMD ignore line: " + line
            }
        }
        Collections.sort(changelogVersionSectionWithContentList, comparator())
    }


    public void writeToMD(File file) {
        File tmp = new File(file.path + "_tmp")
        file.renameTo(tmp)
        StringBuilder sb = new StringBuilder()
        sb.append("# CHANGELOG").append("\n\n")
        for (ChangelogVersionSectionWithContent changelogVersionSectionWithContent : changelogVersionSectionWithContentList) {
            if (changelogVersionSectionWithContent.contents != null && !changelogVersionSectionWithContent.contents.isEmpty()) {
                sb.append("## ").append(changelogVersionSectionWithContent.version.toString()).append("\n\n")
            }
            for (ChangelogTypeWithContent changelogTypeWithContent : changelogVersionSectionWithContent.contents) {
                if (changelogTypeWithContent.changelogContents != null && !changelogTypeWithContent.changelogContents.isEmpty()) {
                    sb.append("### ").append(changelogTypeWithContent.changelogType.name()).append("\n\n")
                }
                for (String content : changelogTypeWithContent.changelogContents) {
                    sb.append("- ").append(content).append("\n")
                }
                sb.append("\n")
            }
        }
        file.write(sb.toString(), "utf-8")
        tmp.delete()
    }

    public String getChangelogOfNewVersion() {
        StringBuilder sb = new StringBuilder()
        for (ChangelogTypeWithContent changelogTypeWithContent : changelogVersionSectionWithContentList.get(0).contents) {
            if (changelogTypeWithContent.changelogContents != null && !changelogTypeWithContent.changelogContents.isEmpty()) {
                sb.append("### ").append(changelogTypeWithContent.changelogType.name()).append("\n\n")
            }
            for (String content : changelogTypeWithContent.changelogContents) {
                sb.append("- ").append(content).append("\n")
            }
            sb.append("\n")
        }
        return sb.toString()
    }

    public Version getNewVersionName() {
        return changelogVersionSectionWithContentList.get(0).version
    }

    private Comparator<ChangelogVersionSectionWithContent> comparator() {
        return new Comparator<ChangelogVersionSectionWithContent>() {
            @Override
            int compare(ChangelogVersionSectionWithContent t, ChangelogVersionSectionWithContent t1) {
                if (t.version.majorVersion > t1.version.majorVersion) {
                    return -1
                } else if (t.version.majorVersion < t1.version.majorVersion) {
                    return 1
                } else {
                    if (t.version.minorVersion > t1.version.minorVersion) {
                        return -1
                    } else if (t.version.minorVersion < t1.version.minorVersion) {
                        return 1
                    } else {
                        if (t.version.buildVersion > t1.version.buildVersion) {
                            return -1
                        } else if (t.version.buildVersion < t1.version.buildVersion) {
                            return 1
                        } else {
                            return 0
                        }
                    }
                }
            }
        }
    }

    public void addVersionChangelog(String version, GitMessageOfChangelog gitMessageOfChangelog) {
        List<ChangelogTypeWithContent> changelogTypeWithContentList = new ArrayList<>()
        changelogTypeWithContentList.add(new ChangelogTypeWithContent(ChangelogType.Changed, gitMessageOfChangelog.changed))
        changelogTypeWithContentList.add(new ChangelogTypeWithContent(ChangelogType.Added, gitMessageOfChangelog.added))
        changelogTypeWithContentList.add(new ChangelogTypeWithContent(ChangelogType.Deprecated, gitMessageOfChangelog.deprecated))
        changelogTypeWithContentList.add(new ChangelogTypeWithContent(ChangelogType.Removed, gitMessageOfChangelog.removed))
        changelogTypeWithContentList.add(new ChangelogTypeWithContent(ChangelogType.Fixed, gitMessageOfChangelog.fixed))
        changelogTypeWithContentList.add(new ChangelogTypeWithContent(ChangelogType.Security, gitMessageOfChangelog.security))
        Version addVersion = new Version(version)
        ChangelogVersionSectionWithContent changelogVersionSectionWithContent = new ChangelogVersionSectionWithContent(addVersion, changelogTypeWithContentList)
        deleteVersionIfExist(addVersion)
        changelogVersionSectionWithContentList.add(0, changelogVersionSectionWithContent)
        Collections.sort(changelogVersionSectionWithContentList, comparator())
    }

    private void deleteVersionIfExist(Version version) {
        int index = findVersion(version)
        if (index >= 0) {
            changelogVersionSectionWithContentList.remove(index)
        }
    }

    private int findVersion(Version version) {
        for (int i = 0; i < changelogVersionSectionWithContentList.size(); i++) {
            if (changelogVersionSectionWithContentList.get(i).version.equals(version)) {
                return i
            }
        }
        return -1
    }

}

class Version {
    public int majorVersion
    public int minorVersion
    public int buildVersion

    public Version(String version) {
        String[] versionPart = String.valueOf(version).split('\\.')
        majorVersion = Integer.parseInt(versionPart[0])
        minorVersion = Integer.parseInt(versionPart[1])
        buildVersion = Integer.parseInt(versionPart[2])
    }

    boolean equals(o) {
        if (this.is(o)) return true
        if (getClass() != o.class) return false

        Version version = (Version) o

        if (buildVersion != version.buildVersion) return false
        if (majorVersion != version.majorVersion) return false
        if (minorVersion != version.minorVersion) return false

        return true
    }

    int hashCode() {
        int result
        result = majorVersion
        result = 31 * result + minorVersion
        result = 31 * result + buildVersion
        return result
    }

    @Override
    public String toString() {
        return majorVersion + "." + minorVersion + "." + buildVersion
    }

    public String toStringOfFileName() {
        return majorVersion + "_" + minorVersion + "_" + buildVersion
    }
}

class GitMessageOfChangelog {
    public static String SHARP = "#"

    public List<String> changed = new ArrayList<>()
    public List<String> added = new ArrayList<>()
    public List<String> deprecated = new ArrayList<>()
    public List<String> removed = new ArrayList<>()
    public List<String> fixed = new ArrayList<>()
    public List<String> security = new ArrayList<>()

    GitMessageOfChangelog(String gitMessages) {
        List<String> lines = gitMessages.readLines()
        List<String> currentTypeList
        for (String line : lines) {
            line = line.trim()
            if (line == null || "" == line) {
                continue
            }
            ChangelogType changelogType = parseChangelogType(line)
            List<String> tmpList = getListOfType(changelogType)
            if (tmpList != null) {
                currentTypeList = tmpList
            } else {
                if (currentTypeList != null) {
                    currentTypeList.add(line)
                }
            }
        }
    }

    private static ChangelogType parseChangelogType(String line) {
        def trimLine = line.trim()
        if (trimLine.startsWith(SHARP) && trimLine.endsWith(SHARP) && trimLine.size() > 2) {
            def type = trimLine.substring(1, trimLine.size() - 1)
            ChangelogType changelogType = ChangelogType.valueOf(type)
            return changelogType
        }
        return null
    }

    private List<String> getListOfType(ChangelogType changelogType) {
        if (changelogType == null) {
            return null
        }
        switch (changelogType) {
            case ChangelogType.Changed:
                return changed
            case ChangelogType.Added:
                return added
            case ChangelogType.Deprecated:
                return deprecated
            case ChangelogType.Removed:
                return removed
            case ChangelogType.Fixed:
                return fixed
            case ChangelogType.Security:
                return security
            default:
                return null
        }
    }

    @Override
    public String toString() {
        return "GitMessageOfChangelog{" +
                "changed=" + changed +
                ", added=" + added +
                ", deprecated=" + deprecated +
                ", removed=" + removed +
                ", fixed=" + fixed +
                ", security=" + security +
                '}';
    }
}

enum ChangelogType {
    Changed,
    Added,
    Deprecated,
    Removed,
    Fixed,
    Security,

    public static List<ChangelogType> CHANGELOG_TYPES = new ArrayList<>()

    static {
        CHANGELOG_TYPES.add(Changed)
        CHANGELOG_TYPES.add(Added)
        CHANGELOG_TYPES.add(Deprecated)
        CHANGELOG_TYPES.add(Removed)
        CHANGELOG_TYPES.add(Fixed)
        CHANGELOG_TYPES.add(Security)
    }
}