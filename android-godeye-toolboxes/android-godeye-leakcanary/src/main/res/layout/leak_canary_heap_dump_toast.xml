<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 Square, Inc.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    >

  <LinearLayout
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:background="@drawable/leak_canary_toast_background"
      android:orientation="vertical"
      android:layout_marginTop="48dp"
      android:padding="16dp"
      >
  <!-- Holds the stop for the image on top-->
  <View
      android:layout_width="@dimen/leak_canary_toast_icon_size"
      android:layout_height="@dimen/leak_canary_toast_icon_size"
      android:layout_gravity="center_horizontal"
      />

  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="center_horizontal"
      android:text="@string/leak_canary_toast_heap_dump"
      android:textColor="#ffffff"
      android:textSize="18sp"
      />
  </LinearLayout>

  <ImageView
      android:id="@+id/leak_canary_toast_icon"
      android:layout_marginTop="@dimen/leak_canary_toast_icon_size"
      android:layout_width="@dimen/leak_canary_toast_icon_size"
      android:layout_height="@dimen/leak_canary_toast_icon_size"
      android:layout_gravity="center_horizontal"
      android:src="@mipmap/androidgodeye_ic_launcher"
      />

</FrameLayout>
