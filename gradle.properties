# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2560m

# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
#org.gradle.parallel=true
#org.gradle.daemon=true
#android.enableAapt2=false
VERSION_METHOD_CANARY=0.15.5
# You don't need to change the version
VERSION_NAME=3.1.11

GROUP=cn.hikyson.godeye

POM_DESCRIPTION=Android God Eye

POM_URL=https://github.com/Kyson/AndroidGodEye
POM_SCM_URL=https://github.com/Kyson/AndroidGodEye
POM_SCM_CONNECTION=scm:git:https://github.com/Kyson/AndroidGodEye.git
POM_SCM_DEV_CONNECTION=scm:**************:Kyson/AndroidGodEye.git

POM_LICENCE_NAME=The Apache Software License, Version 2.0
POM_LICENCE_URL=http://www.apache.org/licenses/LICENSE-2.0.txt
POM_LICENCE_DIST=repo

POM_DEVELOPER_ID=kyson
POM_DEVELOPER_NAME=Kyson
android.useAndroidX=true
android.enableJetifier=true

COMPILE_SDK_VERSION=29
BUILD_TOOLS_VERSION=29.0.2
MIN_SDK_VERSION=16
TARGET_SDK_VERSION=29

USE_ALIYUN_REPO=false
