<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.hikyson.android.godeye.sample.cn.hikyson.godeye.sample.SplashActivity">

    <ImageView
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_above="@+id/splash_tv"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="16dp"
        android:src="@mipmap/ic_launcher"/>

    <TextView
        android:id="@+id/splash_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="Loading..."
        android:textColor="@android:color/holo_green_light"
        android:textSize="18dp"/>

</RelativeLayout>
