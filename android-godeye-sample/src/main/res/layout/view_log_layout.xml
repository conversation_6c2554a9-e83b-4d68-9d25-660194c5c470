<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/view_log_layout_sc"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:descendantFocusability="blocksDescendants"
            android:orientation="vertical">

            <TextView
                android:id="@+id/view_log_layout_log_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#ffffff"
                android:textIsSelectable="true"
                android:textSize="13dp"
                tools:text="I AM LOG..." />
        </LinearLayout>
    </ScrollView>

    <ImageView
        android:id="@+id/view_log_layout_follow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:background="@drawable/ic_follow"
        android:padding="8dp"
        android:src="@drawable/ic_vertical_align_bottom"
        app:layout_constraintEnd_toStartOf="@+id/view_log_layout_clear"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/view_log_layout_clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:padding="8dp"
        android:src="@drawable/ic_delete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</merge>