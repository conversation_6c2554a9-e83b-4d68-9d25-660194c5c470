# AndroidGodEye Monitor Dashboard

## Run dashboard only

1. `cd android-godeye-monitor-dashboard`
2. `npm install` or `cnpm install` if needed.
3. `npm start`

## Build dashboard for app

1. `cd android-godeye-monitor-dashboard`
2. `npm install` or `cnpm install` if needed.
3. [`npm run bd`] or [`npm run build` then `npm run deploy`]
4. run android android-godeye-sample module

## Others

table:https://react-table.js.org/#/story/readme
charts:highcharts
Page_Visibility_API:https://developer.mozilla.org/en-US/docs/Web/API/Page_Visibility_API