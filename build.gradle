// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        if (Boolean.parseBoolean(USE_ALIYUN_REPO)) {
            // Replacement of google()
            maven { url 'http://maven.aliyun.com/nexus/content/repositories/google' }
            // Replacement of mavenCentral()
            maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
            // Replacement of jcenter()
            maven { url 'http://maven.aliyun.com/nexus/content/repositories/jcenter' }
        } else {
            google()
            jcenter()
            mavenCentral()
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.2.1'
        classpath 'com.novoda:bintray-release:0.9.1'
        classpath "cn.hikyson.methodcanary:plugin:${VERSION_METHOD_CANARY}"
        classpath 'com.dicedmelon.gradle:jacoco-android:0.1.4'
    }
}

allprojects {
    repositories {
        if (Boolean.parseBoolean(USE_ALIYUN_REPO)) {
            // Replacement of google()
            maven { url 'http://maven.aliyun.com/nexus/content/repositories/google' }
            // Replacement of mavenCentral()
            maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
            // Replacement of jcenter()
            maven { url 'http://maven.aliyun.com/nexus/content/repositories/jcenter' }
        } else {
            google()
            jcenter()
            mavenCentral()
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

apply from: rootProject.file('gradle/gradle-version-support.gradle')
apply from: rootProject.file('gradle/gradle-changelog-support.gradle')
apply from: rootProject.file('gradle/gradle-github-release-support.gradle')